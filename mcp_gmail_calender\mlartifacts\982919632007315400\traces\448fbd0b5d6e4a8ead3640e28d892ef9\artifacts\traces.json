{"spans": [{"trace_id": "1smdEuGQkQSCNvzAdnJY3Q==", "span_id": "3O3mxH3Aq8c=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749853732516617000, "end_time_unix_nano": 1749853825261696000, "attributes": {"mlflow.spanFunctionName": "\"main\"", "mlflow.spanType": "\"UNKNOWN\"", "mlflow.spanOutputs": "null", "mlflow.traceRequestId": "\"448fbd0b5d6e4a8ead3640e28d892ef9\"", "mlflow.spanInputs": "{}"}, "status": {"message": "", "code": "STATUS_CODE_OK"}}]}