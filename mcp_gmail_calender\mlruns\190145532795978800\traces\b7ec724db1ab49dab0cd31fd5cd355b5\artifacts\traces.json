{"spans": [{"trace_id": "DZ7ZG/v/iyF/YVE1yFJKag==", "span_id": "kcqEIEm7/mU=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749834388520145000, "end_time_unix_nano": 1749834471630416000, "attributes": {"mlflow.spanOutputs": "null", "mlflow.spanInputs": "{}", "mlflow.spanType": "\"UNKNOWN\"", "mlflow.spanFunctionName": "\"main\"", "mlflow.traceRequestId": "\"b7ec724db1ab49dab0cd31fd5cd355b5\""}, "status": {"message": "", "code": "STATUS_CODE_OK"}}]}