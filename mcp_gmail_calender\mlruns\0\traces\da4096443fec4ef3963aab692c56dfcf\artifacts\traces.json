{"spans": [{"trace_id": "x9XLwJClAHiy5IavAkGuqw==", "span_id": "OMDMyjScY6Y=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749831848837647000, "end_time_unix_nano": 1749831851903391000, "attributes": {"mlflow.spanFunctionName": "\"main\"", "mlflow.spanInputs": "{}", "mlflow.traceRequestId": "\"da4096443fec4ef3963aab692c56dfcf\"", "mlflow.spanType": "\"UNKNOWN\""}, "events": [{"time_unix_nano": 1749831851903259000, "name": "exception", "attributes": {"exception.type": "exceptiongroup.ExceptionGroup", "exception.message": "unhandled errors in a TaskGroup (1 sub-exception)", "exception.stacktrace": "  + Exception Group Traceback (most recent call last):\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/opentelemetry/trace/__init__.py\", line 589, in use_span\n  |     yield span\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mlflow/tracing/fluent.py\", line 478, in start_span\n  |     yield mlflow_span\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mlflow/tracing/fluent.py\", line 215, in _wrapping_logic\n  |     result = yield  # sync/async function output to be sent here\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mlflow/tracing/fluent.py\", line 246, in wrapper\n  |     return wrapping_coro.send(await fn(*args, **kwargs))\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/server/langchain_server\", line 28, in main\n  |     tools = await client.get_tools()\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/client.py\", line 137, in get_tools\n  |     tools_list = await asyncio.gather(*load_mcp_tool_tasks)\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/tools.py\", line 133, in load_mcp_tools\n  |     async with create_session(connection) as tool_session:\n  |   File \"/Users/<USER>/anaconda3/lib/python3.10/contextlib.py\", line 217, in __aexit__\n  |     await self.gen.athrow(typ, value, traceback)\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/sessions.py\", line 310, in create_session\n  |     async with _create_stdio_session(\n  |   File \"/Users/<USER>/anaconda3/lib/python3.10/contextlib.py\", line 217, in __aexit__\n  |     await self.gen.athrow(typ, value, traceback)\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/sessions.py\", line 164, in _create_stdio_session\n  |     async with stdio_client(server_params) as (read, write):\n  |   File \"/Users/<USER>/anaconda3/lib/python3.10/contextlib.py\", line 217, in __aexit__\n  |     await self.gen.athrow(typ, value, traceback)\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mcp/client/stdio/__init__.py\", line 172, in stdio_client\n  |     async with (\n  |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 772, in __aexit__\n  |     raise BaseExceptionGroup(\n  | exceptiongroup.ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)\n  +-+---------------- 1 ----------------\n    | Exception Group Traceback (most recent call last):\n    |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mcp/client/stdio/__init__.py\", line 179, in stdio_client\n    |     yield read_stream, write_stream\n    |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/sessions.py\", line 165, in _create_stdio_session\n    |     async with ClientSession(read, write, **(session_kwargs or {})) as session:\n    |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mcp/shared/session.py\", line 218, in __aexit__\n    |     return await self._task_group.__aexit__(exc_type, exc_val, exc_tb)\n    |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 772, in __aexit__\n    |     raise BaseExceptionGroup(\n    | exceptiongroup.ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)\n    +-+---------------- 1 ----------------\n      | Traceback (most recent call last):\n      |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/sessions.py\", line 166, in _create_stdio_session\n      |     yield session\n      |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/sessions.py\", line 321, in create_session\n      |     yield session\n      |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/tools.py\", line 134, in load_mcp_tools\n      |     await tool_session.initialize()\n      |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mcp/client/session.py\", line 123, in initialize\n      |     result = await self.send_request(\n      |   File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mcp/shared/session.py\", line 286, in send_request\n      |     raise McpError(response_or_error.error)\n      | mcp.shared.exceptions.McpError: Connection closed\n      +------------------------------------\n", "exception.escaped": "False"}}], "status": {"message": "ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)", "code": "STATUS_CODE_ERROR"}}]}