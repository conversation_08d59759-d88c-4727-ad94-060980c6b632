{"spans": [{"trace_id": "EluBIYVhUOPrCwAX1RjC0A==", "span_id": "oZIo+TCHzHg=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749831834738663000, "end_time_unix_nano": 1749831834749263000, "attributes": {"mlflow.traceRequestId": "\"35bf17bd8a9e4e52b5a78dd6a6c34e13\"", "mlflow.spanFunctionName": "\"main\"", "mlflow.spanType": "\"UNKNOWN\"", "mlflow.spanInputs": "{}"}, "events": [{"time_unix_nano": 1749831834749194000, "name": "exception", "attributes": {"exception.type": "KeyError", "exception.message": "'transport'", "exception.stacktrace": "Traceback (most recent call last):\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/opentelemetry/trace/__init__.py\", line 589, in use_span\n    yield span\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mlflow/tracing/fluent.py\", line 478, in start_span\n    yield mlflow_span\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mlflow/tracing/fluent.py\", line 215, in _wrapping_logic\n    result = yield  # sync/async function output to be sent here\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mlflow/tracing/fluent.py\", line 246, in wrapper\n    return wrapping_coro.send(await fn(*args, **kwargs))\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/server/langchain_server\", line 28, in main\n    tools = await client.get_tools()\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/client.py\", line 137, in get_tools\n    tools_list = await asyncio.gather(*load_mcp_tool_tasks)\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/tools.py\", line 133, in load_mcp_tools\n    async with create_session(connection) as tool_session:\n  File \"/Users/<USER>/anaconda3/lib/python3.10/contextlib.py\", line 199, in __aenter__\n    return await anext(self.gen)\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/sessions.py\", line 278, in create_session\n    transport = connection[\"transport\"]\nKeyError: 'transport'\n", "exception.escaped": "False"}}], "status": {"message": "KeyError: 'transport'", "code": "STATUS_CODE_ERROR"}}]}