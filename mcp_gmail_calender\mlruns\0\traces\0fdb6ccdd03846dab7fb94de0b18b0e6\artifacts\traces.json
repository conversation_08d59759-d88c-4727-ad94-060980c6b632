{"spans": [{"trace_id": "wQcxOwywuIHWDxgONgJlsA==", "span_id": "zZrOCPsqxlE=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749830571189460000, "end_time_unix_nano": 1749830580911209000, "attributes": {"mlflow.traceRequestId": "\"0fdb6ccdd03846dab7fb94de0b18b0e6\"", "mlflow.spanType": "\"UNKNOWN\"", "mlflow.spanInputs": "{}", "mlflow.spanOutputs": "null", "mlflow.spanFunctionName": "\"main\""}, "status": {"message": "", "code": "STATUS_CODE_OK"}}]}