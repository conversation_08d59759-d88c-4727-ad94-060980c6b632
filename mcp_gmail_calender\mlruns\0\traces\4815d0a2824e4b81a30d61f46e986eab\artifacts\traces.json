{"spans": [{"trace_id": "IPc7Os/rkKRXavxD1M7tlg==", "span_id": "vsJPUXDELuo=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749830518565186000, "end_time_unix_nano": 1749830518593424000, "attributes": {"mlflow.spanType": "\"UNKNOWN\"", "mlflow.spanFunctionName": "\"main\"", "mlflow.spanInputs": "{}", "mlflow.traceRequestId": "\"4815d0a2824e4b81a30d61f46e986eab\""}, "events": [{"time_unix_nano": 1749830518593341000, "name": "exception", "attributes": {"exception.type": "FileNotFoundError", "exception.message": "[Errno 2] No such file or directory: 'npx'", "exception.stacktrace": "Traceback (most recent call last):\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/opentelemetry/trace/__init__.py\", line 589, in use_span\n    yield span\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mlflow/tracing/fluent.py\", line 478, in start_span\n    yield mlflow_span\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mlflow/tracing/fluent.py\", line 215, in _wrapping_logic\n    result = yield  # sync/async function output to be sent here\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mlflow/tracing/fluent.py\", line 246, in wrapper\n    return wrapping_coro.send(await fn(*args, **kwargs))\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/server/langchain_server\", line 73, in main\n    tools = await client.get_tools()\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/client.py\", line 137, in get_tools\n    tools_list = await asyncio.gather(*load_mcp_tool_tasks)\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/tools.py\", line 133, in load_mcp_tools\n    async with create_session(connection) as tool_session:\n  File \"/Users/<USER>/anaconda3/lib/python3.10/contextlib.py\", line 199, in __aenter__\n    return await anext(self.gen)\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/sessions.py\", line 310, in create_session\n    async with _create_stdio_session(\n  File \"/Users/<USER>/anaconda3/lib/python3.10/contextlib.py\", line 199, in __aenter__\n    return await anext(self.gen)\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/langchain_mcp_adapters/sessions.py\", line 164, in _create_stdio_session\n    async with stdio_client(server_params) as (read, write):\n  File \"/Users/<USER>/anaconda3/lib/python3.10/contextlib.py\", line 199, in __aenter__\n    return await anext(self.gen)\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mcp/client/stdio/__init__.py\", line 115, in stdio_client\n    process = await _create_platform_compatible_process(\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/mcp/client/stdio/__init__.py\", line 226, in _create_platform_compatible_process\n    process = await anyio.open_process([command, *args], env=env, stderr=errlog, cwd=cwd)\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/anyio/_core/_subprocesses.py\", line 190, in open_process\n    return await get_async_backend().open_process(\n  File \"/Users/<USER>/Documents/coding/projects/mcp_project/.venv/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 2561, in open_process\n    process = await asyncio.create_subprocess_exec(\n  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/subprocess.py\", line 218, in create_subprocess_exec\n    transport, protocol = await loop.subprocess_exec(\n  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/base_events.py\", line 1675, in subprocess_exec\n    transport = await self._make_subprocess_transport(\n  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/unix_events.py\", line 207, in _make_subprocess_transport\n    transp = _UnixSubprocessTransport(self, protocol, args, shell,\n  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/base_subprocess.py\", line 36, in __init__\n    self._start(args=args, shell=shell, stdin=stdin, stdout=stdout,\n  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/unix_events.py\", line 799, in _start\n    self._proc = subprocess.Popen(\n  File \"/Users/<USER>/anaconda3/lib/python3.10/subprocess.py\", line 971, in __init__\n    self._execute_child(args, executable, preexec_fn, close_fds,\n  File \"/Users/<USER>/anaconda3/lib/python3.10/subprocess.py\", line 1847, in _execute_child\n    raise child_exception_type(errno_num, err_msg, err_filename)\nFileNotFoundError: [Errno 2] No such file or directory: 'npx'\n", "exception.escaped": "False"}}], "status": {"message": "FileNotFoundError: [Errno 2] No such file or directory: 'npx'", "code": "STATUS_CODE_ERROR"}}]}