{"spans": [{"trace_id": "DPOoj6NL6PiFXMAkhChReg==", "span_id": "tkzNaSlg8ZU=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749830778687659000, "end_time_unix_nano": 1749830839208647000, "attributes": {"mlflow.spanOutputs": "null", "mlflow.traceRequestId": "\"15a569b018a04a9ba22f7c8746da9d9c\"", "mlflow.spanInputs": "{}", "mlflow.spanType": "\"UNKNOWN\"", "mlflow.spanFunctionName": "\"main\""}, "status": {"message": "", "code": "STATUS_CODE_OK"}}]}