{"spans": [{"trace_id": "+hMHOzPRxkC0DJ0eSxfASw==", "span_id": "pN/dA3zzPK0=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749830705478575000, "end_time_unix_nano": 1749830712982536000, "attributes": {"mlflow.spanType": "\"UNKNOWN\"", "mlflow.spanFunctionName": "\"main\"", "mlflow.spanInputs": "{}", "mlflow.spanOutputs": "null", "mlflow.traceRequestId": "\"1767f1050dab43449e26b770123a902e\""}, "status": {"message": "", "code": "STATUS_CODE_OK"}}]}