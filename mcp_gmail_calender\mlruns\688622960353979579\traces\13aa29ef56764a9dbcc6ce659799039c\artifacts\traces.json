{"spans": [{"trace_id": "luQl7uOufUGn2tHdKL0uaQ==", "span_id": "NG+zdO3639c=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749834210727853000, "end_time_unix_nano": 1749834264873253000, "attributes": {"mlflow.spanOutputs": "null", "mlflow.spanType": "\"UNKNOWN\"", "mlflow.spanInputs": "{}", "mlflow.traceRequestId": "\"13aa29ef56764a9dbcc6ce659799039c\"", "mlflow.spanFunctionName": "\"main\""}, "status": {"message": "", "code": "STATUS_CODE_OK"}}]}