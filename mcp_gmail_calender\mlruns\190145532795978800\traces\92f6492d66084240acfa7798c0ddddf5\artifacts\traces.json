{"spans": [{"trace_id": "aAD4Xf1JInI1cYt3JUh/YQ==", "span_id": "q2gtwjLjBK4=", "trace_state": "", "parent_span_id": "", "name": "main", "start_time_unix_nano": 1749835186191912000, "end_time_unix_nano": 1749836122194693000, "attributes": {"mlflow.spanOutputs": "null", "mlflow.spanFunctionName": "\"main\"", "mlflow.spanInputs": "{}", "mlflow.traceRequestId": "\"92f6492d66084240acfa7798c0ddddf5\"", "mlflow.spanType": "\"UNKNOWN\""}, "status": {"message": "", "code": "STATUS_CODE_OK"}}]}