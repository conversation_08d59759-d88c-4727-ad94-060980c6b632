{"spans": [{"trace_id": "TCY4PNjvvHSb1Z1eVe8oOQ==", "span_id": "S19Fo1wFhVs=", "trace_state": "", "parent_span_id": "", "name": "LangGraph", "start_time_unix_nano": 1749854569996675000, "end_time_unix_nano": 1749854571604206000, "attributes": {"mlflow.spanType": "\"CHAIN\"", "mlflow.traceRequestId": "\"be1cb28521de46ef9731d04fb2fa610f\"", "mlflow.spanInputs": "{\"messages\": [{\"role\": \"user\", \"content\": \"\"}]}", "mlflow.spanOutputs": "{\"messages\": [{\"content\": \"\", \"additional_kwargs\": {}, \"response_metadata\": {}, \"type\": \"human\", \"name\": null, \"id\": \"943663f5-eaea-4841-9fa5-79c264e547e0\", \"example\": false}, {\"content\": \"I'm ready to assist with your Gmail or Calendar tasks. Please let me know what you'd like to accomplish, such as:\\n\\n- Searching for emails\\n- Creating or updating events\\n- Managing drafts\\n- Sending messages\\n- Setting up filters\\n- Managing labels\\n- Or other tasks\\n\\nJust describe what you need done, and I'll handle the details!\", \"additional_kwargs\": {}, \"response_metadata\": {\"token_usage\": {\"completion_tokens\": 225, \"prompt_tokens\": 8767, \"total_tokens\": 8992, \"completion_time\": 0.627065759, \"prompt_time\": 0.488391837, \"queue_time\": 0.12180704200000003, \"total_time\": 1.115457596}, \"model_name\": \"qwen/qwen3-32b\", \"system_fingerprint\": \"fp_8487b76fd3\", \"finish_reason\": \"stop\", \"logprobs\": null}, \"type\": \"ai\", \"name\": null, \"id\": \"run--4ba9b623-66a2-40ca-89f6-f737c1762e08-0\", \"example\": false, \"tool_calls\": [], \"invalid_tool_calls\": [], \"usage_metadata\": {\"input_tokens\": 8767, \"output_tokens\": 225, \"total_tokens\": 8992}}]}"}, "status": {"message": "", "code": "STATUS_CODE_OK"}}, {"trace_id": "TCY4PNjvvHSb1Z1eVe8oOQ==", "span_id": "0kVnyOpm2U0=", "trace_state": "", "parent_span_id": "S19Fo1wFhVs=", "name": "agent", "start_time_unix_nano": 1749854569998209000, "end_time_unix_nano": 1749854571602670000, "attributes": {"metadata": "{\"langgraph_step\": 1, \"langgraph_node\": \"agent\", \"langgraph_triggers\": [\"branch:to:agent\"], \"langgraph_path\": [\"__pregel_pull\", \"agent\"], \"langgraph_checkpoint_ns\": \"agent:8c5dd3a1-e6a4-02a9-ee91-a0e369015f89\"}", "mlflow.spanType": "\"CHAIN\"", "mlflow.traceRequestId": "\"be1cb28521de46ef9731d04fb2fa610f\"", "mlflow.spanInputs": "{\"messages\": [{\"content\": \"\", \"additional_kwargs\": {}, \"response_metadata\": {}, \"type\": \"human\", \"name\": null, \"id\": \"943663f5-eaea-4841-9fa5-79c264e547e0\", \"example\": false}], \"is_last_step\": false, \"remaining_steps\": 24}", "mlflow.spanOutputs": "{\"messages\": [{\"content\": \"I'm ready to assist with your Gmail or Calendar tasks. Please let me know what you'd like to accomplish, such as:\\n\\n- Searching for emails\\n- Creating or updating events\\n- Managing drafts\\n- Sending messages\\n- Setting up filters\\n- Managing labels\\n- Or other tasks\\n\\nJust describe what you need done, and I'll handle the details!\", \"additional_kwargs\": {}, \"response_metadata\": {\"token_usage\": {\"completion_tokens\": 225, \"prompt_tokens\": 8767, \"total_tokens\": 8992, \"completion_time\": 0.627065759, \"prompt_time\": 0.488391837, \"queue_time\": 0.12180704200000003, \"total_time\": 1.115457596}, \"model_name\": \"qwen/qwen3-32b\", \"system_fingerprint\": \"fp_8487b76fd3\", \"finish_reason\": \"stop\", \"logprobs\": null}, \"type\": \"ai\", \"name\": null, \"id\": \"run--4ba9b623-66a2-40ca-89f6-f737c1762e08-0\", \"example\": false, \"tool_calls\": [], \"invalid_tool_calls\": [], \"usage_metadata\": {\"input_tokens\": 8767, \"output_tokens\": 225, \"total_tokens\": 8992}}]}"}, "status": {"message": "", "code": "STATUS_CODE_OK"}}, {"trace_id": "TCY4PNjvvHSb1Z1eVe8oOQ==", "span_id": "mwgIa/VtnH4=", "trace_state": "", "parent_span_id": "0kVnyOpm2U0=", "name": "call_model", "start_time_unix_nano": 1749854569999026000, "end_time_unix_nano": 1749854571598259000, "attributes": {"metadata": "{\"langgraph_step\": 1, \"langgraph_node\": \"agent\", \"langgraph_triggers\": [\"branch:to:agent\"], \"langgraph_path\": [\"__pregel_pull\", \"agent\"], \"langgraph_checkpoint_ns\": \"agent:8c5dd3a1-e6a4-02a9-ee91-a0e369015f89\"}", "mlflow.spanType": "\"CHAIN\"", "mlflow.traceRequestId": "\"be1cb28521de46ef9731d04fb2fa610f\"", "mlflow.spanInputs": "{\"messages\": [{\"content\": \"\", \"additional_kwargs\": {}, \"response_metadata\": {}, \"type\": \"human\", \"name\": null, \"id\": \"943663f5-eaea-4841-9fa5-79c264e547e0\", \"example\": false}], \"is_last_step\": false, \"remaining_steps\": 24}", "mlflow.spanOutputs": "{\"messages\": [{\"content\": \"I'm ready to assist with your Gmail or Calendar tasks. Please let me know what you'd like to accomplish, such as:\\n\\n- Searching for emails\\n- Creating or updating events\\n- Managing drafts\\n- Sending messages\\n- Setting up filters\\n- Managing labels\\n- Or other tasks\\n\\nJust describe what you need done, and I'll handle the details!\", \"additional_kwargs\": {}, \"response_metadata\": {\"token_usage\": {\"completion_tokens\": 225, \"prompt_tokens\": 8767, \"total_tokens\": 8992, \"completion_time\": 0.627065759, \"prompt_time\": 0.488391837, \"queue_time\": 0.12180704200000003, \"total_time\": 1.115457596}, \"model_name\": \"qwen/qwen3-32b\", \"system_fingerprint\": \"fp_8487b76fd3\", \"finish_reason\": \"stop\", \"logprobs\": null}, \"type\": \"ai\", \"name\": null, \"id\": \"run--4ba9b623-66a2-40ca-89f6-f737c1762e08-0\", \"example\": false, \"tool_calls\": [], \"invalid_tool_calls\": [], \"usage_metadata\": {\"input_tokens\": 8767, \"output_tokens\": 225, \"total_tokens\": 8992}}]}"}, "status": {"message": "", "code": "STATUS_CODE_OK"}}, {"trace_id": "TCY4PNjvvHSb1Z1eVe8oOQ==", "span_id": "rZBvIo1kCPs=", "trace_state": "", "parent_span_id": "0kVnyOpm2U0=", "name": "RunnableSequence", "start_time_unix_nano": 1749854570000394000, "end_time_unix_nano": 1749854571597096000, "attributes": {"metadata": "{\"langgraph_step\": 1, \"langgraph_node\": \"agent\", \"langgraph_triggers\": [\"branch:to:agent\"], \"langgraph_path\": [\"__pregel_pull\", \"agent\"], \"langgraph_checkpoint_ns\": \"agent:8c5dd3a1-e6a4-02a9-ee91-a0e369015f89\", \"checkpoint_ns\": \"agent:8c5dd3a1-e6a4-02a9-ee91-a0e369015f89\"}", "mlflow.spanType": "\"CHAIN\"", "mlflow.traceRequestId": "\"be1cb28521de46ef9731d04fb2fa610f\"", "mlflow.spanInputs": "{\"messages\": [{\"content\": \"\", \"additional_kwargs\": {}, \"response_metadata\": {}, \"type\": \"human\", \"name\": null, \"id\": \"943663f5-eaea-4841-9fa5-79c264e547e0\", \"example\": false}], \"is_last_step\": false, \"remaining_steps\": 24}", "mlflow.spanOutputs": "{\"content\": \"I'm ready to assist with your Gmail or Calendar tasks. Please let me know what you'd like to accomplish, such as:\\n\\n- Searching for emails\\n- Creating or updating events\\n- Managing drafts\\n- Sending messages\\n- Setting up filters\\n- Managing labels\\n- Or other tasks\\n\\nJust describe what you need done, and I'll handle the details!\", \"additional_kwargs\": {}, \"response_metadata\": {\"token_usage\": {\"completion_tokens\": 225, \"prompt_tokens\": 8767, \"total_tokens\": 8992, \"completion_time\": 0.627065759, \"prompt_time\": 0.488391837, \"queue_time\": 0.12180704200000003, \"total_time\": 1.115457596}, \"model_name\": \"qwen/qwen3-32b\", \"system_fingerprint\": \"fp_8487b76fd3\", \"finish_reason\": \"stop\", \"logprobs\": null}, \"type\": \"ai\", \"name\": null, \"id\": \"run--4ba9b623-66a2-40ca-89f6-f737c1762e08-0\", \"example\": false, \"tool_calls\": [], \"invalid_tool_calls\": [], \"usage_metadata\": {\"input_tokens\": 8767, \"output_tokens\": 225, \"total_tokens\": 8992}}"}, "status": {"message": "", "code": "STATUS_CODE_OK"}}, {"trace_id": "TCY4PNjvvHSb1Z1eVe8oOQ==", "span_id": "JLDD/T0zNU8=", "trace_state": "", "parent_span_id": "rZBvIo1kCPs=", "name": "Prompt", "start_time_unix_nano": 1749854570001248000, "end_time_unix_nano": 1749854570001922000, "attributes": {"metadata": "{\"langgraph_step\": 1, \"langgraph_node\": \"agent\", \"langgraph_triggers\": [\"branch:to:agent\"], \"langgraph_path\": [\"__pregel_pull\", \"agent\"], \"langgraph_checkpoint_ns\": \"agent:8c5dd3a1-e6a4-02a9-ee91-a0e369015f89\", \"checkpoint_ns\": \"agent:8c5dd3a1-e6a4-02a9-ee91-a0e369015f89\"}", "mlflow.spanType": "\"CHAIN\"", "mlflow.traceRequestId": "\"be1cb28521de46ef9731d04fb2fa610f\"", "mlflow.spanInputs": "{\"messages\": [{\"content\": \"\", \"additional_kwargs\": {}, \"response_metadata\": {}, \"type\": \"human\", \"name\": null, \"id\": \"943663f5-eaea-4841-9fa5-79c264e547e0\", \"example\": false}], \"is_last_step\": false, \"remaining_steps\": 24}", "mlflow.spanOutputs": "[{\"content\": \"\", \"additional_kwargs\": {}, \"response_metadata\": {}, \"type\": \"human\", \"name\": null, \"id\": \"943663f5-eaea-4841-9fa5-79c264e547e0\", \"example\": false}]"}, "status": {"message": "", "code": "STATUS_CODE_OK"}}, {"trace_id": "TCY4PNjvvHSb1Z1eVe8oOQ==", "span_id": "DUEWeNynWco=", "trace_state": "", "parent_span_id": "rZBvIo1kCPs=", "name": "ChatGroq", "start_time_unix_nano": 1749854570005031000, "end_time_unix_nano": 1749854571595692000, "attributes": {"mlflow.spanOutputs": "{\"generations\": [[{\"text\": \"I'm ready to assist with your Gmail or Calendar tasks. Please let me know what you'd like to accomplish, such as:\\n\\n- Searching for emails\\n- Creating or updating events\\n- Managing drafts\\n- Sending messages\\n- Setting up filters\\n- Managing labels\\n- Or other tasks\\n\\nJust describe what you need done, and I'll handle the details!\", \"generation_info\": {\"finish_reason\": \"stop\", \"logprobs\": null}, \"type\": \"ChatGeneration\", \"message\": {\"content\": \"I'm ready to assist with your Gmail or Calendar tasks. Please let me know what you'd like to accomplish, such as:\\n\\n- Searching for emails\\n- Creating or updating events\\n- Managing drafts\\n- Sending messages\\n- Setting up filters\\n- Managing labels\\n- Or other tasks\\n\\nJust describe what you need done, and I'll handle the details!\", \"additional_kwargs\": {}, \"response_metadata\": {\"token_usage\": {\"completion_tokens\": 225, \"prompt_tokens\": 8767, \"total_tokens\": 8992, \"completion_time\": 0.627065759, \"prompt_time\": 0.488391837, \"queue_time\": 0.12180704200000003, \"total_time\": 1.115457596}, \"model_name\": \"qwen/qwen3-32b\", \"system_fingerprint\": \"fp_8487b76fd3\", \"finish_reason\": \"stop\", \"logprobs\": null}, \"type\": \"ai\", \"name\": null, \"id\": \"run--4ba9b623-66a2-40ca-89f6-f737c1762e08-0\"}}]], \"llm_output\": {\"token_usage\": {\"completion_tokens\": 225, \"prompt_tokens\": 8767, \"total_tokens\": 8992, \"completion_time\": 0.627065759, \"prompt_time\": 0.488391837, \"queue_time\": 0.12180704200000003, \"total_time\": 1.115457596}, \"model_name\": \"qwen/qwen3-32b\", \"system_fingerprint\": \"fp_8487b76fd3\"}, \"run\": null, \"type\": \"LLMResult\"}", "batch_size": "1", "mlflow.chat.tools": "[{\"type\": \"function\", \"function\": {\"name\": \"search\", \"description\": \"\\n    Search DuckDuckGo and return formatted results.\\n\\n    Args:\\n        query: The search query string\\n        max_results: Maximum number of results to return (default: 10)\\n        ctx: MCP context for logging\\n    \", \"parameters\": {\"properties\": {\"query\": {\"type\": \"string\"}, \"max_results\": {\"type\": \"integer\"}}, \"type\": \"object\", \"required\": [\"query\"]}}}, {\"type\": \"function\", \"function\": {\"name\": \"fetch_content\", \"description\": \"\\n    Fetch and parse content from a webpage URL.\\n\\n    Args:\\n        url: The webpage URL to fetch content from\\n        ctx: MCP context for logging\\n    \", \"parameters\": {\"properties\": {\"url\": {\"type\": \"string\"}}, \"type\": \"object\", \"required\": [\"url\"]}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_event\", \"description\": \"Create a new Google Calendar event\", \"parameters\": {\"properties\": {\"summary\": {\"type\": \"string\", \"description\": \"Event title\"}, \"description\": {\"type\": \"string\", \"description\": \"Event description\"}, \"startTime\": {\"type\": \"string\", \"description\": \"Event start time (ISO format)\"}, \"endTime\": {\"type\": \"string\", \"description\": \"Event end time (ISO format)\"}, \"attendees\": {\"type\": \"array\", \"description\": \"List of attendee email addresses\", \"items\": {\"type\": \"string\"}}}, \"type\": \"object\", \"required\": [\"summary\", \"startTime\", \"endTime\"]}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_events\", \"description\": \"List Google Calendar events\", \"parameters\": {\"properties\": {\"timeMin\": {\"type\": \"string\", \"description\": \"Start time (ISO format)\"}, \"maxResults\": {\"type\": \"number\", \"description\": \"Maximum number of results\"}}, \"type\": \"object\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_event\", \"description\": \"Update an existing Google Calendar event\", \"parameters\": {\"properties\": {\"eventId\": {\"type\": \"string\", \"description\": \"ID of the event to update\"}, \"updates\": {\"type\": \"object\"}}, \"type\": \"object\", \"required\": [\"eventId\", \"updates\"]}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_event\", \"description\": \"Delete a Google Calendar event\", \"parameters\": {\"properties\": {\"eventId\": {\"type\": \"string\", \"description\": \"ID of the event to delete\"}}, \"type\": \"object\", \"required\": [\"eventId\"]}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_draft\", \"description\": \"Create a draft email in Gmail. Note the mechanics of the raw parameter.\", \"parameters\": {\"properties\": {\"raw\": {\"type\": \"string\", \"description\": \"The entire email message in base64url encoded RFC 2822 format, ignores params.to, cc, bcc, subject, body, includeBodyHtml if provided\"}, \"threadId\": {\"type\": \"string\", \"description\": \"The thread ID to associate this draft with\"}, \"to\": {\"type\": \"array\", \"description\": \"List of recipient email addresses\", \"items\": {\"type\": \"string\"}}, \"cc\": {\"type\": \"array\", \"description\": \"List of CC recipient email addresses\", \"items\": {\"type\": \"string\"}}, \"bcc\": {\"type\": \"array\", \"description\": \"List of BCC recipient email addresses\", \"items\": {\"type\": \"string\"}}, \"subject\": {\"type\": \"string\", \"description\": \"The subject of the email\"}, \"body\": {\"type\": \"string\", \"description\": \"The body of the email\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_draft\", \"description\": \"Delete a draft\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the draft to delete\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_draft\", \"description\": \"Get a specific draft by ID\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the draft to retrieve\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_drafts\", \"description\": \"List drafts in the user's mailbox\", \"parameters\": {\"properties\": {\"maxResults\": {\"type\": \"number\", \"description\": \"Maximum number of drafts to return. Accepts values between 1-500\"}, \"q\": {\"type\": \"string\", \"description\": \"Only return drafts matching the specified query. Supports the same query format as the Gmail search box\"}, \"includeSpamTrash\": {\"type\": \"boolean\", \"description\": \"Include drafts from SPAM and TRASH in the results\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"send_draft\", \"description\": \"Send an existing draft\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the draft to send\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_label\", \"description\": \"Create a new label\", \"parameters\": {\"properties\": {\"name\": {\"type\": \"string\", \"description\": \"The display name of the label\"}, \"messageListVisibility\": {\"type\": \"string\", \"description\": \"The visibility of messages with this label in the message list\", \"enum\": [\"show\", \"hide\"]}, \"labelListVisibility\": {\"type\": \"string\", \"description\": \"The visibility of the label in the label list\", \"enum\": [\"labelShow\", \"labelShowIfUnread\", \"labelHide\"]}, \"color\": {\"type\": \"object\", \"description\": \"The color settings for the label\"}}, \"type\": \"object\", \"required\": [\"name\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_label\", \"description\": \"Delete a label\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the label to delete\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_label\", \"description\": \"Get a specific label by ID\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the label to retrieve\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_labels\", \"description\": \"List all labels in the user's mailbox\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"patch_label\", \"description\": \"Patch an existing label (partial update)\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the label to patch\"}, \"name\": {\"type\": \"string\", \"description\": \"The display name of the label\"}, \"messageListVisibility\": {\"type\": \"string\", \"description\": \"The visibility of messages with this label in the message list\", \"enum\": [\"show\", \"hide\"]}, \"labelListVisibility\": {\"type\": \"string\", \"description\": \"The visibility of the label in the label list\", \"enum\": [\"labelShow\", \"labelShowIfUnread\", \"labelHide\"]}, \"color\": {\"type\": \"object\", \"description\": \"The color settings for the label\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_label\", \"description\": \"Update an existing label\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the label to update\"}, \"name\": {\"type\": \"string\", \"description\": \"The display name of the label\"}, \"messageListVisibility\": {\"type\": \"string\", \"description\": \"The visibility of messages with this label in the message list\", \"enum\": [\"show\", \"hide\"]}, \"labelListVisibility\": {\"type\": \"string\", \"description\": \"The visibility of the label in the label list\", \"enum\": [\"labelShow\", \"labelShowIfUnread\", \"labelHide\"]}, \"color\": {\"type\": \"object\", \"description\": \"The color settings for the label\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"batch_delete_messages\", \"description\": \"Delete multiple messages\", \"parameters\": {\"properties\": {\"ids\": {\"type\": \"array\", \"description\": \"The IDs of the messages to delete\", \"items\": {\"type\": \"string\"}}}, \"type\": \"object\", \"required\": [\"ids\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"batch_modify_messages\", \"description\": \"Modify the labels on multiple messages\", \"parameters\": {\"properties\": {\"ids\": {\"type\": \"array\", \"description\": \"The IDs of the messages to modify\", \"items\": {\"type\": \"string\"}}, \"addLabelIds\": {\"type\": \"array\", \"description\": \"A list of label IDs to add to the messages\", \"items\": {\"type\": \"string\"}}, \"removeLabelIds\": {\"type\": \"array\", \"description\": \"A list of label IDs to remove from the messages\", \"items\": {\"type\": \"string\"}}}, \"type\": \"object\", \"required\": [\"ids\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_message\", \"description\": \"Immediately and permanently delete a message\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to delete\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_message\", \"description\": \"Get a specific message by ID with format options\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to retrieve\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_messages\", \"description\": \"List messages in the user's mailbox with optional filtering\", \"parameters\": {\"properties\": {\"maxResults\": {\"type\": \"number\", \"description\": \"Maximum number of messages to return. Accepts values between 1-500\"}, \"pageToken\": {\"type\": \"string\", \"description\": \"Page token to retrieve a specific page of results\"}, \"q\": {\"type\": \"string\", \"description\": \"Only return messages matching the specified query. Supports the same query format as the Gmail search box\"}, \"labelIds\": {\"type\": \"array\", \"description\": \"Only return messages with labels that match all of the specified label IDs\", \"items\": {\"type\": \"string\"}}, \"includeSpamTrash\": {\"type\": \"boolean\", \"description\": \"Include messages from SPAM and TRASH in the results\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"modify_message\", \"description\": \"Modify the labels on a message\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to modify\"}, \"addLabelIds\": {\"type\": \"array\", \"description\": \"A list of label IDs to add to the message\", \"items\": {\"type\": \"string\"}}, \"removeLabelIds\": {\"type\": \"array\", \"description\": \"A list of label IDs to remove from the message\", \"items\": {\"type\": \"string\"}}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"send_message\", \"description\": \"Send an email message to specified recipients. Note the mechanics of the raw parameter.\", \"parameters\": {\"properties\": {\"raw\": {\"type\": \"string\", \"description\": \"The entire email message in base64url encoded RFC 2822 format, ignores params.to, cc, bcc, subject, body, includeBodyHtml if provided\"}, \"threadId\": {\"type\": \"string\", \"description\": \"The thread ID to associate this message with\"}, \"to\": {\"type\": \"array\", \"description\": \"List of recipient email addresses\", \"items\": {\"type\": \"string\"}}, \"cc\": {\"type\": \"array\", \"description\": \"List of CC recipient email addresses\", \"items\": {\"type\": \"string\"}}, \"bcc\": {\"type\": \"array\", \"description\": \"List of BCC recipient email addresses\", \"items\": {\"type\": \"string\"}}, \"subject\": {\"type\": \"string\", \"description\": \"The subject of the email\"}, \"body\": {\"type\": \"string\", \"description\": \"The body of the email\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"trash_message\", \"description\": \"Move a message to the trash\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to move to trash\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"untrash_message\", \"description\": \"Remove a message from the trash\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to remove from trash\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_attachment\", \"description\": \"Get a message attachment\", \"parameters\": {\"properties\": {\"messageId\": {\"type\": \"string\", \"description\": \"ID of the message containing the attachment\"}, \"id\": {\"type\": \"string\", \"description\": \"The ID of the attachment\"}}, \"type\": \"object\", \"required\": [\"messageId\", \"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_thread\", \"description\": \"Delete a thread\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to delete\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_thread\", \"description\": \"Get a specific thread by ID\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to retrieve\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_threads\", \"description\": \"List threads in the user's mailbox\", \"parameters\": {\"properties\": {\"maxResults\": {\"type\": \"number\", \"description\": \"Maximum number of threads to return\"}, \"pageToken\": {\"type\": \"string\", \"description\": \"Page token to retrieve a specific page of results\"}, \"q\": {\"type\": \"string\", \"description\": \"Only return threads matching the specified query\"}, \"labelIds\": {\"type\": \"array\", \"description\": \"Only return threads with labels that match all of the specified label IDs\", \"items\": {\"type\": \"string\"}}, \"includeSpamTrash\": {\"type\": \"boolean\", \"description\": \"Include threads from SPAM and TRASH in the results\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"modify_thread\", \"description\": \"Modify the labels applied to a thread\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to modify\"}, \"addLabelIds\": {\"type\": \"array\", \"description\": \"A list of label IDs to add to the thread\", \"items\": {\"type\": \"string\"}}, \"removeLabelIds\": {\"type\": \"array\", \"description\": \"A list of label IDs to remove from the thread\", \"items\": {\"type\": \"string\"}}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"trash_thread\", \"description\": \"Move a thread to the trash\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to move to trash\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"untrash_thread\", \"description\": \"Remove a thread from the trash\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to remove from trash\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_auto_forwarding\", \"description\": \"Gets auto-forwarding settings\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_imap\", \"description\": \"Gets IMAP settings\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_language\", \"description\": \"Gets language settings\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_pop\", \"description\": \"Gets POP settings\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_vacation\", \"description\": \"Get vacation responder settings\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_auto_forwarding\", \"description\": \"Updates automatic forwarding settings\", \"parameters\": {\"properties\": {\"enabled\": {\"type\": \"boolean\", \"description\": \"Whether all incoming mail is automatically forwarded to another address\"}, \"emailAddress\": {\"type\": \"string\", \"description\": \"Email address to which messages should be automatically forwarded\"}, \"disposition\": {\"type\": \"string\", \"description\": \"The state in which messages should be left after being forwarded\", \"enum\": [\"leaveInInbox\", \"archive\", \"trash\", \"markRead\"]}}, \"type\": \"object\", \"required\": [\"enabled\", \"emailAddress\", \"disposition\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_imap\", \"description\": \"Updates IMAP settings\", \"parameters\": {\"properties\": {\"enabled\": {\"type\": \"boolean\", \"description\": \"Whether IMAP is enabled for the account\"}, \"expungeBehavior\": {\"type\": \"string\", \"description\": \"The action that will be executed on a message when it is marked as deleted and expunged from the last visible IMAP folder\", \"enum\": [\"archive\", \"trash\", \"deleteForever\"]}, \"maxFolderSize\": {\"type\": \"number\", \"description\": \"An optional limit on the number of messages that can be accessed through IMAP\"}}, \"type\": \"object\", \"required\": [\"enabled\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_language\", \"description\": \"Updates language settings\", \"parameters\": {\"properties\": {\"displayLanguage\": {\"type\": \"string\", \"description\": \"The language to display Gmail in, formatted as an RFC 3066 Language Tag\"}}, \"type\": \"object\", \"required\": [\"displayLanguage\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_pop\", \"description\": \"Updates POP settings\", \"parameters\": {\"properties\": {\"accessWindow\": {\"type\": \"string\", \"description\": \"The range of messages which are accessible via POP\", \"enum\": [\"disabled\", \"allMail\", \"fromNowOn\"]}, \"disposition\": {\"type\": \"string\", \"description\": \"The action that will be executed on a message after it has been fetched via POP\", \"enum\": [\"archive\", \"trash\", \"leaveInInbox\"]}}, \"type\": \"object\", \"required\": [\"accessWindow\", \"disposition\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_vacation\", \"description\": \"Update vacation responder settings\", \"parameters\": {\"properties\": {\"enableAutoReply\": {\"type\": \"boolean\", \"description\": \"Whether the vacation responder is enabled\"}, \"responseSubject\": {\"type\": \"string\", \"description\": \"Optional subject line for the vacation responder auto-reply\"}, \"responseBodyPlainText\": {\"type\": \"string\", \"description\": \"Response body in plain text format\"}, \"restrictToContacts\": {\"type\": \"boolean\", \"description\": \"Whether responses are only sent to contacts\"}, \"restrictToDomain\": {\"type\": \"boolean\", \"description\": \"Whether responses are only sent to users in the same domain\"}, \"startTime\": {\"type\": \"string\", \"description\": \"Start time for sending auto-replies (epoch ms)\"}, \"endTime\": {\"type\": \"string\", \"description\": \"End time for sending auto-replies (epoch ms)\"}}, \"type\": \"object\", \"required\": [\"enableAutoReply\", \"responseBodyPlainText\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"add_delegate\", \"description\": \"Adds a delegate to the specified account\", \"parameters\": {\"properties\": {\"delegateEmail\": {\"type\": \"string\", \"description\": \"Email address of delegate to add\"}}, \"type\": \"object\", \"required\": [\"delegateEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"remove_delegate\", \"description\": \"Removes the specified delegate\", \"parameters\": {\"properties\": {\"delegateEmail\": {\"type\": \"string\", \"description\": \"Email address of delegate to remove\"}}, \"type\": \"object\", \"required\": [\"delegateEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_delegate\", \"description\": \"Gets the specified delegate\", \"parameters\": {\"properties\": {\"delegateEmail\": {\"type\": \"string\", \"description\": \"The email address of the delegate to retrieve\"}}, \"type\": \"object\", \"required\": [\"delegateEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_delegates\", \"description\": \"Lists the delegates for the specified account\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_filter\", \"description\": \"Creates a filter\", \"parameters\": {\"properties\": {\"criteria\": {\"type\": \"object\", \"description\": \"Filter criteria\"}, \"action\": {\"type\": \"object\", \"description\": \"Actions to perform on messages matching the criteria\"}}, \"type\": \"object\", \"required\": [\"criteria\", \"action\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_filter\", \"description\": \"Deletes a filter\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the filter to be deleted\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_filter\", \"description\": \"Gets a filter\", \"parameters\": {\"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the filter to be fetched\"}}, \"type\": \"object\", \"required\": [\"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_filters\", \"description\": \"Lists the message filters of a Gmail user\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_forwarding_address\", \"description\": \"Creates a forwarding address\", \"parameters\": {\"properties\": {\"forwardingEmail\": {\"type\": \"string\", \"description\": \"An email address to which messages can be forwarded\"}}, \"type\": \"object\", \"required\": [\"forwardingEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_forwarding_address\", \"description\": \"Deletes the specified forwarding address\", \"parameters\": {\"properties\": {\"forwardingEmail\": {\"type\": \"string\", \"description\": \"The forwarding address to be deleted\"}}, \"type\": \"object\", \"required\": [\"forwardingEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_forwarding_address\", \"description\": \"Gets the specified forwarding address\", \"parameters\": {\"properties\": {\"forwardingEmail\": {\"type\": \"string\", \"description\": \"The forwarding address to be retrieved\"}}, \"type\": \"object\", \"required\": [\"forwardingEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_forwarding_addresses\", \"description\": \"Lists the forwarding addresses for the specified account\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_send_as\", \"description\": \"Creates a custom send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"displayName\": {\"type\": \"string\", \"description\": \"A name that appears in the 'From:' header\"}, \"replyToAddress\": {\"type\": \"string\", \"description\": \"An optional email address that is included in a 'Reply-To:' header\"}, \"signature\": {\"type\": \"string\", \"description\": \"An optional HTML signature\"}, \"isPrimary\": {\"type\": \"boolean\", \"description\": \"Whether this address is the primary address\"}, \"treatAsAlias\": {\"type\": \"boolean\", \"description\": \"Whether Gmail should treat this address as an alias\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_send_as\", \"description\": \"Deletes the specified send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be deleted\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_send_as\", \"description\": \"Gets the specified send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be retrieved\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_send_as\", \"description\": \"Lists the send-as aliases for the specified account\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"patch_send_as\", \"description\": \"Patches the specified send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be updated\"}, \"displayName\": {\"type\": \"string\", \"description\": \"A name that appears in the 'From:' header\"}, \"replyToAddress\": {\"type\": \"string\", \"description\": \"An optional email address that is included in a 'Reply-To:' header\"}, \"signature\": {\"type\": \"string\", \"description\": \"An optional HTML signature\"}, \"isPrimary\": {\"type\": \"boolean\", \"description\": \"Whether this address is the primary address\"}, \"treatAsAlias\": {\"type\": \"boolean\", \"description\": \"Whether Gmail should treat this address as an alias\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_send_as\", \"description\": \"Updates a send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be updated\"}, \"displayName\": {\"type\": \"string\", \"description\": \"A name that appears in the 'From:' header\"}, \"replyToAddress\": {\"type\": \"string\", \"description\": \"An optional email address that is included in a 'Reply-To:' header\"}, \"signature\": {\"type\": \"string\", \"description\": \"An optional HTML signature\"}, \"isPrimary\": {\"type\": \"boolean\", \"description\": \"Whether this address is the primary address\"}, \"treatAsAlias\": {\"type\": \"boolean\", \"description\": \"Whether Gmail should treat this address as an alias\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"verify_send_as\", \"description\": \"Sends a verification email to the specified send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be verified\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_smime_info\", \"description\": \"Deletes the specified S/MIME config for the specified send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"id\": {\"type\": \"string\", \"description\": \"The immutable ID for the S/MIME config\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\", \"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_smime_info\", \"description\": \"Gets the specified S/MIME config for the specified send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"id\": {\"type\": \"string\", \"description\": \"The immutable ID for the S/MIME config\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\", \"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"insert_smime_info\", \"description\": \"Insert (upload) the given S/MIME config for the specified send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"encryptedKeyPassword\": {\"type\": \"string\", \"description\": \"Encrypted key password\"}, \"pkcs12\": {\"type\": \"string\", \"description\": \"PKCS#12 format containing a single private/public key pair and certificate chain\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\", \"encryptedKeyPassword\", \"pkcs12\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_smime_info\", \"description\": \"Lists S/MIME configs for the specified send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"set_default_smime_info\", \"description\": \"Sets the default S/MIME config for the specified send-as alias\", \"parameters\": {\"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"id\": {\"type\": \"string\", \"description\": \"The immutable ID for the S/MIME config\"}}, \"type\": \"object\", \"required\": [\"sendAsEmail\", \"id\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_profile\", \"description\": \"Get the current user's Gmail profile\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"watch_mailbox\", \"description\": \"Watch for changes to the user's mailbox\", \"parameters\": {\"properties\": {\"topicName\": {\"type\": \"string\", \"description\": \"The name of the Cloud Pub/Sub topic to publish notifications to\"}, \"labelIds\": {\"type\": \"array\", \"description\": \"Label IDs to restrict notifications to\", \"items\": {\"type\": \"string\"}}, \"labelFilterAction\": {\"type\": \"string\", \"description\": \"Whether to include or exclude the specified labels\", \"enum\": [\"include\", \"exclude\"]}}, \"type\": \"object\", \"required\": [\"topicName\"], \"additionalProperties\": false}}}, {\"type\": \"function\", \"function\": {\"name\": \"stop_mail_watch\", \"description\": \"Stop receiving push notifications for the given user mailbox\", \"parameters\": {\"properties\": {}, \"type\": \"object\", \"additionalProperties\": false}}}]", "mlflow.traceRequestId": "\"be1cb28521de46ef9731d04fb2fa610f\"", "options": "{\"stop\": null}", "mlflow.spanInputs": "[[{\"content\": \"\", \"additional_kwargs\": {}, \"response_metadata\": {}, \"type\": \"human\", \"name\": null, \"id\": \"943663f5-eaea-4841-9fa5-79c264e547e0\", \"example\": false}]]", "mlflow.chat.messages": "[{\"role\": \"user\", \"content\": \"\"}, {\"role\": \"assistant\", \"content\": \"I'm ready to assist with your Gmail or Calendar tasks. Please let me know what you'd like to accomplish, such as:\\n\\n- Searching for emails\\n- Creating or updating events\\n- Managing drafts\\n- Sending messages\\n- Setting up filters\\n- Managing labels\\n- Or other tasks\\n\\nJust describe what you need done, and I'll handle the details!\"}]", "metadata": "{\"langgraph_step\": 1, \"langgraph_node\": \"agent\", \"langgraph_triggers\": [\"branch:to:agent\"], \"langgraph_path\": [\"__pregel_pull\", \"agent\"], \"langgraph_checkpoint_ns\": \"agent:8c5dd3a1-e6a4-02a9-ee91-a0e369015f89\", \"checkpoint_ns\": \"agent:8c5dd3a1-e6a4-02a9-ee91-a0e369015f89\", \"ls_provider\": \"groq\", \"ls_model_name\": \"qwen/qwen3-32b\", \"ls_model_type\": \"chat\", \"ls_temperature\": 0.6, \"ls_max_tokens\": 4096}", "mlflow.spanType": "\"CHAT_MODEL\"", "invocation_params": "{\"_type\": \"groq-chat\", \"stop\": null, \"tools\": [{\"type\": \"function\", \"function\": {\"name\": \"search\", \"description\": \"\\n    Search DuckDuckGo and return formatted results.\\n\\n    Args:\\n        query: The search query string\\n        max_results: Maximum number of results to return (default: 10)\\n        ctx: MCP context for logging\\n    \", \"parameters\": {\"properties\": {\"query\": {\"type\": \"string\"}, \"max_results\": {\"default\": 10, \"type\": \"integer\"}}, \"required\": [\"query\"], \"type\": \"object\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"fetch_content\", \"description\": \"\\n    Fetch and parse content from a webpage URL.\\n\\n    Args:\\n        url: The webpage URL to fetch content from\\n        ctx: MCP context for logging\\n    \", \"parameters\": {\"properties\": {\"url\": {\"type\": \"string\"}}, \"required\": [\"url\"], \"type\": \"object\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_event\", \"description\": \"Create a new Google Calendar event\", \"parameters\": {\"type\": \"object\", \"properties\": {\"summary\": {\"type\": \"string\", \"description\": \"Event title\"}, \"description\": {\"type\": \"string\", \"description\": \"Event description\"}, \"startTime\": {\"type\": \"string\", \"description\": \"Event start time (ISO format)\"}, \"endTime\": {\"type\": \"string\", \"description\": \"Event end time (ISO format)\"}, \"attendees\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"List of attendee email addresses\"}}, \"required\": [\"summary\", \"startTime\", \"endTime\"]}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_events\", \"description\": \"List Google Calendar events\", \"parameters\": {\"type\": \"object\", \"properties\": {\"timeMin\": {\"type\": \"string\", \"description\": \"Start time (ISO format)\"}, \"maxResults\": {\"type\": \"number\", \"description\": \"Maximum number of results\"}}}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_event\", \"description\": \"Update an existing Google Calendar event\", \"parameters\": {\"type\": \"object\", \"properties\": {\"eventId\": {\"type\": \"string\", \"description\": \"ID of the event to update\"}, \"updates\": {\"type\": \"object\", \"properties\": {\"summary\": {\"type\": \"string\", \"description\": \"New event title\"}, \"description\": {\"type\": \"string\", \"description\": \"New event description\"}, \"startTime\": {\"type\": \"string\", \"description\": \"New start time\"}, \"endTime\": {\"type\": \"string\", \"description\": \"New end time\"}, \"attendees\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"New list of attendees\"}}}}, \"required\": [\"eventId\", \"updates\"]}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_event\", \"description\": \"Delete a Google Calendar event\", \"parameters\": {\"type\": \"object\", \"properties\": {\"eventId\": {\"type\": \"string\", \"description\": \"ID of the event to delete\"}}, \"required\": [\"eventId\"]}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_draft\", \"description\": \"Create a draft email in Gmail. Note the mechanics of the raw parameter.\", \"parameters\": {\"type\": \"object\", \"properties\": {\"raw\": {\"type\": \"string\", \"description\": \"The entire email message in base64url encoded RFC 2822 format, ignores params.to, cc, bcc, subject, body, includeBodyHtml if provided\"}, \"threadId\": {\"type\": \"string\", \"description\": \"The thread ID to associate this draft with\"}, \"to\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"List of recipient email addresses\"}, \"cc\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"List of CC recipient email addresses\"}, \"bcc\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"List of BCC recipient email addresses\"}, \"subject\": {\"type\": \"string\", \"description\": \"The subject of the email\"}, \"body\": {\"type\": \"string\", \"description\": \"The body of the email\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_draft\", \"description\": \"Delete a draft\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the draft to delete\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_draft\", \"description\": \"Get a specific draft by ID\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the draft to retrieve\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_drafts\", \"description\": \"List drafts in the user's mailbox\", \"parameters\": {\"type\": \"object\", \"properties\": {\"maxResults\": {\"type\": \"number\", \"description\": \"Maximum number of drafts to return. Accepts values between 1-500\"}, \"q\": {\"type\": \"string\", \"description\": \"Only return drafts matching the specified query. Supports the same query format as the Gmail search box\"}, \"includeSpamTrash\": {\"type\": \"boolean\", \"description\": \"Include drafts from SPAM and TRASH in the results\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"send_draft\", \"description\": \"Send an existing draft\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the draft to send\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_label\", \"description\": \"Create a new label\", \"parameters\": {\"type\": \"object\", \"properties\": {\"name\": {\"type\": \"string\", \"description\": \"The display name of the label\"}, \"messageListVisibility\": {\"type\": \"string\", \"enum\": [\"show\", \"hide\"], \"description\": \"The visibility of messages with this label in the message list\"}, \"labelListVisibility\": {\"type\": \"string\", \"enum\": [\"labelShow\", \"labelShowIfUnread\", \"labelHide\"], \"description\": \"The visibility of the label in the label list\"}, \"color\": {\"type\": \"object\", \"properties\": {\"textColor\": {\"type\": \"string\", \"description\": \"The text color of the label as hex string\"}, \"backgroundColor\": {\"type\": \"string\", \"description\": \"The background color of the label as hex string\"}}, \"required\": [\"textColor\", \"backgroundColor\"], \"additionalProperties\": false, \"description\": \"The color settings for the label\"}}, \"required\": [\"name\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_label\", \"description\": \"Delete a label\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the label to delete\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_label\", \"description\": \"Get a specific label by ID\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the label to retrieve\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_labels\", \"description\": \"List all labels in the user's mailbox\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"patch_label\", \"description\": \"Patch an existing label (partial update)\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the label to patch\"}, \"name\": {\"type\": \"string\", \"description\": \"The display name of the label\"}, \"messageListVisibility\": {\"type\": \"string\", \"enum\": [\"show\", \"hide\"], \"description\": \"The visibility of messages with this label in the message list\"}, \"labelListVisibility\": {\"type\": \"string\", \"enum\": [\"labelShow\", \"labelShowIfUnread\", \"labelHide\"], \"description\": \"The visibility of the label in the label list\"}, \"color\": {\"type\": \"object\", \"properties\": {\"textColor\": {\"type\": \"string\", \"description\": \"The text color of the label as hex string\"}, \"backgroundColor\": {\"type\": \"string\", \"description\": \"The background color of the label as hex string\"}}, \"required\": [\"textColor\", \"backgroundColor\"], \"additionalProperties\": false, \"description\": \"The color settings for the label\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_label\", \"description\": \"Update an existing label\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the label to update\"}, \"name\": {\"type\": \"string\", \"description\": \"The display name of the label\"}, \"messageListVisibility\": {\"type\": \"string\", \"enum\": [\"show\", \"hide\"], \"description\": \"The visibility of messages with this label in the message list\"}, \"labelListVisibility\": {\"type\": \"string\", \"enum\": [\"labelShow\", \"labelShowIfUnread\", \"labelHide\"], \"description\": \"The visibility of the label in the label list\"}, \"color\": {\"type\": \"object\", \"properties\": {\"textColor\": {\"type\": \"string\", \"description\": \"The text color of the label as hex string\"}, \"backgroundColor\": {\"type\": \"string\", \"description\": \"The background color of the label as hex string\"}}, \"required\": [\"textColor\", \"backgroundColor\"], \"additionalProperties\": false, \"description\": \"The color settings for the label\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"batch_delete_messages\", \"description\": \"Delete multiple messages\", \"parameters\": {\"type\": \"object\", \"properties\": {\"ids\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"The IDs of the messages to delete\"}}, \"required\": [\"ids\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"batch_modify_messages\", \"description\": \"Modify the labels on multiple messages\", \"parameters\": {\"type\": \"object\", \"properties\": {\"ids\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"The IDs of the messages to modify\"}, \"addLabelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"A list of label IDs to add to the messages\"}, \"removeLabelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"A list of label IDs to remove from the messages\"}}, \"required\": [\"ids\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_message\", \"description\": \"Immediately and permanently delete a message\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to delete\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_message\", \"description\": \"Get a specific message by ID with format options\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to retrieve\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_messages\", \"description\": \"List messages in the user's mailbox with optional filtering\", \"parameters\": {\"type\": \"object\", \"properties\": {\"maxResults\": {\"type\": \"number\", \"description\": \"Maximum number of messages to return. Accepts values between 1-500\"}, \"pageToken\": {\"type\": \"string\", \"description\": \"Page token to retrieve a specific page of results\"}, \"q\": {\"type\": \"string\", \"description\": \"Only return messages matching the specified query. Supports the same query format as the Gmail search box\"}, \"labelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"Only return messages with labels that match all of the specified label IDs\"}, \"includeSpamTrash\": {\"type\": \"boolean\", \"description\": \"Include messages from SPAM and TRASH in the results\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"modify_message\", \"description\": \"Modify the labels on a message\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to modify\"}, \"addLabelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"A list of label IDs to add to the message\"}, \"removeLabelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"A list of label IDs to remove from the message\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"send_message\", \"description\": \"Send an email message to specified recipients. Note the mechanics of the raw parameter.\", \"parameters\": {\"type\": \"object\", \"properties\": {\"raw\": {\"type\": \"string\", \"description\": \"The entire email message in base64url encoded RFC 2822 format, ignores params.to, cc, bcc, subject, body, includeBodyHtml if provided\"}, \"threadId\": {\"type\": \"string\", \"description\": \"The thread ID to associate this message with\"}, \"to\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"List of recipient email addresses\"}, \"cc\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"List of CC recipient email addresses\"}, \"bcc\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"List of BCC recipient email addresses\"}, \"subject\": {\"type\": \"string\", \"description\": \"The subject of the email\"}, \"body\": {\"type\": \"string\", \"description\": \"The body of the email\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"trash_message\", \"description\": \"Move a message to the trash\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to move to trash\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"untrash_message\", \"description\": \"Remove a message from the trash\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the message to remove from trash\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_attachment\", \"description\": \"Get a message attachment\", \"parameters\": {\"type\": \"object\", \"properties\": {\"messageId\": {\"type\": \"string\", \"description\": \"ID of the message containing the attachment\"}, \"id\": {\"type\": \"string\", \"description\": \"The ID of the attachment\"}}, \"required\": [\"messageId\", \"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_thread\", \"description\": \"Delete a thread\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to delete\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_thread\", \"description\": \"Get a specific thread by ID\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to retrieve\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_threads\", \"description\": \"List threads in the user's mailbox\", \"parameters\": {\"type\": \"object\", \"properties\": {\"maxResults\": {\"type\": \"number\", \"description\": \"Maximum number of threads to return\"}, \"pageToken\": {\"type\": \"string\", \"description\": \"Page token to retrieve a specific page of results\"}, \"q\": {\"type\": \"string\", \"description\": \"Only return threads matching the specified query\"}, \"labelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"Only return threads with labels that match all of the specified label IDs\"}, \"includeSpamTrash\": {\"type\": \"boolean\", \"description\": \"Include threads from SPAM and TRASH in the results\"}, \"includeBodyHtml\": {\"type\": \"boolean\", \"description\": \"Whether to include the parsed HTML in the return for each body, excluded by default because they can be excessively large\"}}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"modify_thread\", \"description\": \"Modify the labels applied to a thread\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to modify\"}, \"addLabelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"A list of label IDs to add to the thread\"}, \"removeLabelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"A list of label IDs to remove from the thread\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"trash_thread\", \"description\": \"Move a thread to the trash\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to move to trash\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"untrash_thread\", \"description\": \"Remove a thread from the trash\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the thread to remove from trash\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_auto_forwarding\", \"description\": \"Gets auto-forwarding settings\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_imap\", \"description\": \"Gets IMAP settings\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_language\", \"description\": \"Gets language settings\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_pop\", \"description\": \"Gets POP settings\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_vacation\", \"description\": \"Get vacation responder settings\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_auto_forwarding\", \"description\": \"Updates automatic forwarding settings\", \"parameters\": {\"type\": \"object\", \"properties\": {\"enabled\": {\"type\": \"boolean\", \"description\": \"Whether all incoming mail is automatically forwarded to another address\"}, \"emailAddress\": {\"type\": \"string\", \"description\": \"Email address to which messages should be automatically forwarded\"}, \"disposition\": {\"type\": \"string\", \"enum\": [\"leaveInInbox\", \"archive\", \"trash\", \"markRead\"], \"description\": \"The state in which messages should be left after being forwarded\"}}, \"required\": [\"enabled\", \"emailAddress\", \"disposition\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_imap\", \"description\": \"Updates IMAP settings\", \"parameters\": {\"type\": \"object\", \"properties\": {\"enabled\": {\"type\": \"boolean\", \"description\": \"Whether IMAP is enabled for the account\"}, \"expungeBehavior\": {\"type\": \"string\", \"enum\": [\"archive\", \"trash\", \"deleteForever\"], \"description\": \"The action that will be executed on a message when it is marked as deleted and expunged from the last visible IMAP folder\"}, \"maxFolderSize\": {\"type\": \"number\", \"description\": \"An optional limit on the number of messages that can be accessed through IMAP\"}}, \"required\": [\"enabled\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_language\", \"description\": \"Updates language settings\", \"parameters\": {\"type\": \"object\", \"properties\": {\"displayLanguage\": {\"type\": \"string\", \"description\": \"The language to display Gmail in, formatted as an RFC 3066 Language Tag\"}}, \"required\": [\"displayLanguage\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_pop\", \"description\": \"Updates POP settings\", \"parameters\": {\"type\": \"object\", \"properties\": {\"accessWindow\": {\"type\": \"string\", \"enum\": [\"disabled\", \"allMail\", \"fromNowOn\"], \"description\": \"The range of messages which are accessible via POP\"}, \"disposition\": {\"type\": \"string\", \"enum\": [\"archive\", \"trash\", \"leaveInInbox\"], \"description\": \"The action that will be executed on a message after it has been fetched via POP\"}}, \"required\": [\"accessWindow\", \"disposition\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_vacation\", \"description\": \"Update vacation responder settings\", \"parameters\": {\"type\": \"object\", \"properties\": {\"enableAutoReply\": {\"type\": \"boolean\", \"description\": \"Whether the vacation responder is enabled\"}, \"responseSubject\": {\"type\": \"string\", \"description\": \"Optional subject line for the vacation responder auto-reply\"}, \"responseBodyPlainText\": {\"type\": \"string\", \"description\": \"Response body in plain text format\"}, \"restrictToContacts\": {\"type\": \"boolean\", \"description\": \"Whether responses are only sent to contacts\"}, \"restrictToDomain\": {\"type\": \"boolean\", \"description\": \"Whether responses are only sent to users in the same domain\"}, \"startTime\": {\"type\": \"string\", \"description\": \"Start time for sending auto-replies (epoch ms)\"}, \"endTime\": {\"type\": \"string\", \"description\": \"End time for sending auto-replies (epoch ms)\"}}, \"required\": [\"enableAutoReply\", \"responseBodyPlainText\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"add_delegate\", \"description\": \"Adds a delegate to the specified account\", \"parameters\": {\"type\": \"object\", \"properties\": {\"delegateEmail\": {\"type\": \"string\", \"description\": \"Email address of delegate to add\"}}, \"required\": [\"delegateEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"remove_delegate\", \"description\": \"Removes the specified delegate\", \"parameters\": {\"type\": \"object\", \"properties\": {\"delegateEmail\": {\"type\": \"string\", \"description\": \"Email address of delegate to remove\"}}, \"required\": [\"delegateEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_delegate\", \"description\": \"Gets the specified delegate\", \"parameters\": {\"type\": \"object\", \"properties\": {\"delegateEmail\": {\"type\": \"string\", \"description\": \"The email address of the delegate to retrieve\"}}, \"required\": [\"delegateEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_delegates\", \"description\": \"Lists the delegates for the specified account\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_filter\", \"description\": \"Creates a filter\", \"parameters\": {\"type\": \"object\", \"properties\": {\"criteria\": {\"type\": \"object\", \"properties\": {\"from\": {\"type\": \"string\", \"description\": \"The sender's display name or email address\"}, \"to\": {\"type\": \"string\", \"description\": \"The recipient's display name or email address\"}, \"subject\": {\"type\": \"string\", \"description\": \"Case-insensitive phrase in the message's subject\"}, \"query\": {\"type\": \"string\", \"description\": \"A Gmail search query that specifies the filter's criteria\"}, \"negatedQuery\": {\"type\": \"string\", \"description\": \"A Gmail search query that specifies criteria the message must not match\"}, \"hasAttachment\": {\"type\": \"boolean\", \"description\": \"Whether the message has any attachment\"}, \"excludeChats\": {\"type\": \"boolean\", \"description\": \"Whether the response should exclude chats\"}, \"size\": {\"type\": \"number\", \"description\": \"The size of the entire RFC822 message in bytes\"}, \"sizeComparison\": {\"type\": \"string\", \"enum\": [\"smaller\", \"larger\"], \"description\": \"How the message size in bytes should be in relation to the size field\"}}, \"additionalProperties\": false, \"description\": \"Filter criteria\"}, \"action\": {\"type\": \"object\", \"properties\": {\"addLabelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"List of labels to add to messages\"}, \"removeLabelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"List of labels to remove from messages\"}, \"forward\": {\"type\": \"string\", \"description\": \"Email address that the message should be forwarded to\"}}, \"additionalProperties\": false, \"description\": \"Actions to perform on messages matching the criteria\"}}, \"required\": [\"criteria\", \"action\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_filter\", \"description\": \"Deletes a filter\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the filter to be deleted\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_filter\", \"description\": \"Gets a filter\", \"parameters\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"description\": \"The ID of the filter to be fetched\"}}, \"required\": [\"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_filters\", \"description\": \"Lists the message filters of a Gmail user\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_forwarding_address\", \"description\": \"Creates a forwarding address\", \"parameters\": {\"type\": \"object\", \"properties\": {\"forwardingEmail\": {\"type\": \"string\", \"description\": \"An email address to which messages can be forwarded\"}}, \"required\": [\"forwardingEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_forwarding_address\", \"description\": \"Deletes the specified forwarding address\", \"parameters\": {\"type\": \"object\", \"properties\": {\"forwardingEmail\": {\"type\": \"string\", \"description\": \"The forwarding address to be deleted\"}}, \"required\": [\"forwardingEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_forwarding_address\", \"description\": \"Gets the specified forwarding address\", \"parameters\": {\"type\": \"object\", \"properties\": {\"forwardingEmail\": {\"type\": \"string\", \"description\": \"The forwarding address to be retrieved\"}}, \"required\": [\"forwardingEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_forwarding_addresses\", \"description\": \"Lists the forwarding addresses for the specified account\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"create_send_as\", \"description\": \"Creates a custom send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"displayName\": {\"type\": \"string\", \"description\": \"A name that appears in the 'From:' header\"}, \"replyToAddress\": {\"type\": \"string\", \"description\": \"An optional email address that is included in a 'Reply-To:' header\"}, \"signature\": {\"type\": \"string\", \"description\": \"An optional HTML signature\"}, \"isPrimary\": {\"type\": \"boolean\", \"description\": \"Whether this address is the primary address\"}, \"treatAsAlias\": {\"type\": \"boolean\", \"description\": \"Whether Gmail should treat this address as an alias\"}}, \"required\": [\"sendAsEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_send_as\", \"description\": \"Deletes the specified send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be deleted\"}}, \"required\": [\"sendAsEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_send_as\", \"description\": \"Gets the specified send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be retrieved\"}}, \"required\": [\"sendAsEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_send_as\", \"description\": \"Lists the send-as aliases for the specified account\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"patch_send_as\", \"description\": \"Patches the specified send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be updated\"}, \"displayName\": {\"type\": \"string\", \"description\": \"A name that appears in the 'From:' header\"}, \"replyToAddress\": {\"type\": \"string\", \"description\": \"An optional email address that is included in a 'Reply-To:' header\"}, \"signature\": {\"type\": \"string\", \"description\": \"An optional HTML signature\"}, \"isPrimary\": {\"type\": \"boolean\", \"description\": \"Whether this address is the primary address\"}, \"treatAsAlias\": {\"type\": \"boolean\", \"description\": \"Whether Gmail should treat this address as an alias\"}}, \"required\": [\"sendAsEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"update_send_as\", \"description\": \"Updates a send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be updated\"}, \"displayName\": {\"type\": \"string\", \"description\": \"A name that appears in the 'From:' header\"}, \"replyToAddress\": {\"type\": \"string\", \"description\": \"An optional email address that is included in a 'Reply-To:' header\"}, \"signature\": {\"type\": \"string\", \"description\": \"An optional HTML signature\"}, \"isPrimary\": {\"type\": \"boolean\", \"description\": \"Whether this address is the primary address\"}, \"treatAsAlias\": {\"type\": \"boolean\", \"description\": \"Whether Gmail should treat this address as an alias\"}}, \"required\": [\"sendAsEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"verify_send_as\", \"description\": \"Sends a verification email to the specified send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The send-as alias to be verified\"}}, \"required\": [\"sendAsEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"delete_smime_info\", \"description\": \"Deletes the specified S/MIME config for the specified send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"id\": {\"type\": \"string\", \"description\": \"The immutable ID for the S/MIME config\"}}, \"required\": [\"sendAsEmail\", \"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_smime_info\", \"description\": \"Gets the specified S/MIME config for the specified send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"id\": {\"type\": \"string\", \"description\": \"The immutable ID for the S/MIME config\"}}, \"required\": [\"sendAsEmail\", \"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"insert_smime_info\", \"description\": \"Insert (upload) the given S/MIME config for the specified send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"encryptedKeyPassword\": {\"type\": \"string\", \"description\": \"Encrypted key password\"}, \"pkcs12\": {\"type\": \"string\", \"description\": \"PKCS#12 format containing a single private/public key pair and certificate chain\"}}, \"required\": [\"sendAsEmail\", \"encryptedKeyPassword\", \"pkcs12\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"list_smime_info\", \"description\": \"Lists S/MIME configs for the specified send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}}, \"required\": [\"sendAsEmail\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"set_default_smime_info\", \"description\": \"Sets the default S/MIME config for the specified send-as alias\", \"parameters\": {\"type\": \"object\", \"properties\": {\"sendAsEmail\": {\"type\": \"string\", \"description\": \"The email address that appears in the 'From:' header\"}, \"id\": {\"type\": \"string\", \"description\": \"The immutable ID for the S/MIME config\"}}, \"required\": [\"sendAsEmail\", \"id\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"get_profile\", \"description\": \"Get the current user's Gmail profile\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"watch_mailbox\", \"description\": \"Watch for changes to the user's mailbox\", \"parameters\": {\"type\": \"object\", \"properties\": {\"topicName\": {\"type\": \"string\", \"description\": \"The name of the Cloud Pub/Sub topic to publish notifications to\"}, \"labelIds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"description\": \"Label IDs to restrict notifications to\"}, \"labelFilterAction\": {\"type\": \"string\", \"enum\": [\"include\", \"exclude\"], \"description\": \"Whether to include or exclude the specified labels\"}}, \"required\": [\"topicName\"], \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}, {\"type\": \"function\", \"function\": {\"name\": \"stop_mail_watch\", \"description\": \"Stop receiving push notifications for the given user mailbox\", \"parameters\": {\"type\": \"object\", \"properties\": {}, \"additionalProperties\": false, \"$schema\": \"http://json-schema.org/draft-07/schema#\"}}}]}", "mlflow.chat.tokenUsage": "{\"input_tokens\": 8767, \"output_tokens\": 225, \"total_tokens\": 8992}"}, "status": {"message": "", "code": "STATUS_CODE_OK"}}, {"trace_id": "TCY4PNjvvHSb1Z1eVe8oOQ==", "span_id": "tjaR6YGeXhI=", "trace_state": "", "parent_span_id": "0kVnyOpm2U0=", "name": "should_continue", "start_time_unix_nano": 1749854571600159000, "end_time_unix_nano": 1749854571601666000, "attributes": {"metadata": "{\"langgraph_step\": 1, \"langgraph_node\": \"agent\", \"langgraph_triggers\": [\"branch:to:agent\"], \"langgraph_path\": [\"__pregel_pull\", \"agent\"], \"langgraph_checkpoint_ns\": \"agent:8c5dd3a1-e6a4-02a9-ee91-a0e369015f89\"}", "mlflow.spanType": "\"CHAIN\"", "mlflow.traceRequestId": "\"be1cb28521de46ef9731d04fb2fa610f\"", "mlflow.spanInputs": "{\"messages\": [{\"content\": \"\", \"additional_kwargs\": {}, \"response_metadata\": {}, \"type\": \"human\", \"name\": null, \"id\": \"943663f5-eaea-4841-9fa5-79c264e547e0\", \"example\": false}, {\"content\": \"I'm ready to assist with your Gmail or Calendar tasks. Please let me know what you'd like to accomplish, such as:\\n\\n- Searching for emails\\n- Creating or updating events\\n- Managing drafts\\n- Sending messages\\n- Setting up filters\\n- Managing labels\\n- Or other tasks\\n\\nJust describe what you need done, and I'll handle the details!\", \"additional_kwargs\": {}, \"response_metadata\": {\"token_usage\": {\"completion_tokens\": 225, \"prompt_tokens\": 8767, \"total_tokens\": 8992, \"completion_time\": 0.627065759, \"prompt_time\": 0.488391837, \"queue_time\": 0.12180704200000003, \"total_time\": 1.115457596}, \"model_name\": \"qwen/qwen3-32b\", \"system_fingerprint\": \"fp_8487b76fd3\", \"finish_reason\": \"stop\", \"logprobs\": null}, \"type\": \"ai\", \"name\": null, \"id\": \"run--4ba9b623-66a2-40ca-89f6-f737c1762e08-0\", \"example\": false, \"tool_calls\": [], \"invalid_tool_calls\": [], \"usage_metadata\": {\"input_tokens\": 8767, \"output_tokens\": 225, \"total_tokens\": 8992}}], \"is_last_step\": false, \"remaining_steps\": 24}", "mlflow.spanOutputs": "\"__end__\""}, "status": {"message": "", "code": "STATUS_CODE_OK"}}]}